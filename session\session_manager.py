"""
Session manager for handling authentication and session lifecycle.
"""

import json
import os
import logging
from datetime import datetime
from typing import Optional
import threading

from config import config
from .session_models import SessionData, AuthResponse

logger = logging.getLogger(__name__)


class SessionManager:
    """Manages authentication sessions and lifecycle."""

    def __init__(self):
        # Delayed import to avoid circular dependency
        from brain_api_client import BrainAPIClient
        self.api_client = BrainAPIClient()
        self.session_file = config.SESSION_FILE_PATH
        self._current_session: Optional[SessionData] = None
        self._lock = threading.Lock()
        self._load_session()

    def _load_session(self) -> None:
        """Load session from disk if it exists and is valid."""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, 'r') as f:
                    data = json.load(f)

                session = SessionData(
                    token=data['token'],
                    expiry=datetime.fromisoformat(data['expiry']),
                    user_id=data.get('user_id'),
                    created_at=datetime.fromisoformat(data['created_at'])
                )

                if not session.is_expired:
                    self._current_session = session
                    logger.info("Loaded valid session from disk")
                else:
                    logger.info("Session on disk is expired")
                    os.remove(self.session_file)

        except Exception as e:
            logger.error(f"Error loading session: {e}")
            if os.path.exists(self.session_file):
                os.remove(self.session_file)

    def _save_session(self, session: SessionData) -> None:
        """Save session to disk."""
        try:
            data = {
                'token': session.token,
                'expiry': session.expiry.isoformat(),
                'user_id': session.user_id,
                'created_at': session.created_at.isoformat()
            }

            with open(self.session_file, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info("Session saved to disk")

        except Exception as e:
            logger.error(f"Error saving session: {e}")

    def _create_and_save_session(self, auth_response: AuthResponse, success_message: str) -> None:
        """
        Create and save session from successful authentication response.

        Args:
            auth_response: Successful authentication response
            success_message: Message to log on success
        """
        session = SessionData(
            token=auth_response.token,
            expiry=auth_response.expiry,
            created_at=datetime.now()
        )

        self._current_session = session
        self._save_session(session)
        logger.info(success_message)

    def authenticate(self) -> AuthResponse:
        """
        Perform authentication with Brain API.

        Returns:
            AuthResponse with authentication result
        """
        with self._lock:
            logger.info("Starting authentication")

            auth_response = self.api_client.authenticate(
                config.BRAIN_USER,
                config.BRAIN_PASSWORD
            )

            if auth_response.success:
                self._create_and_save_session(
                    auth_response, "Authentication successful")

            return auth_response

    def complete_biometric_auth(self, biometric_url: str) -> AuthResponse:
        """
        Complete biometric authentication.

        Args:
            biometric_url: The persona URL for biometric auth

        Returns:
            AuthResponse with final result
        """
        with self._lock:
            logger.info("Completing biometric authentication")

            auth_response = self.api_client.complete_biometric_auth(
                biometric_url)

            if auth_response.success:
                self._create_and_save_session(
                    auth_response, "Biometric authentication successful")

            return auth_response

    def get_session(self) -> Optional[SessionData]:
        """
        Get current valid session.

        Returns:
            SessionData if valid session exists, None otherwise
        """
        with self._lock:
            if self._current_session and not self._current_session.is_expired:
                return self._current_session

            # Session expired or doesn't exist
            if self._current_session:
                logger.info("Current session is expired")
                self._current_session = None
                if os.path.exists(self.session_file):
                    os.remove(self.session_file)

            return None

    def is_authenticated(self) -> bool:
        """Check if we have a valid session."""
        return self.get_session() is not None

    def get_session_status(self) -> dict:
        """
        Get detailed session status.

        Returns:
            Dictionary with session status information
        """
        session = self.get_session()

        if not session:
            return {
                'authenticated': False,
                'status': 'No active session'
            }

        minutes_left = session.minutes_until_expiry
        hours = minutes_left // 60
        mins = minutes_left % 60

        return {
            'authenticated': True,
            'status': 'Active',
            'expires_at': session.expiry.strftime('%Y-%m-%d %H:%M:%S'),
            'time_remaining': f"{hours:02d}:{mins:02d}",
            'minutes_remaining': minutes_left
        }

    def logout(self) -> None:
        """Clear current session."""
        with self._lock:
            self._current_session = None
            if os.path.exists(self.session_file):
                os.remove(self.session_file)
            logger.info("Session cleared")

    def get_authenticated_session(self):
        """Get requests session with authentication."""
        session = self.get_session()
        if session:
            return self.api_client.get_authenticated_session(session.token)
        return None

    def check_expiry_notifications(self) -> list[int]:
        """
        Check if session needs expiry notifications.

        Returns:
            List of minutes before expiry that need notification
        """
        session = self.get_session()
        if not session:
            return []

        notifications_needed = []
        minutes_left = session.minutes_until_expiry

        for notify_minutes in config.NOTIFY_BEFORE_EXPIRY_MINUTES:
            if minutes_left == notify_minutes:
                notifications_needed.append(notify_minutes)

        return notifications_needed


# Global session manager instance (lazy initialization)
_session_manager_instance = None


class SessionManagerProxy:
    """Proxy class for lazy initialization of session manager."""

    def __getattr__(self, name):
        global _session_manager_instance
        if _session_manager_instance is None:
            _session_manager_instance = SessionManager()
        return getattr(_session_manager_instance, name)


# Global session manager instance
session_manager_instance = SessionManagerProxy()
