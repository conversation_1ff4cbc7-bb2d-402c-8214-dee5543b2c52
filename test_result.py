from brain_api_client import BrainAP<PERSON><PERSON>
from session.session_manager import session_manager_instance
import json

def test_authenticated_request():
    """Test making an authenticated request to the Brain API."""
    client = BrainAPIClient()

    # Get current session
    session = session_manager_instance.get_session()

    try:
        # Make authenticated request using the token from the session
        response = client.make_authenticated_request("GET", "/alphas/1326mdJ/", session.token)

        print(f"✅ Request successful!")
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ API request completed successfully")
            try:
                data = response.json()
                with open("./result/alpha_id.json","w") as f:
                    json.dump(data,f,indent=4)
                # print(f"Response Data: \n{json.dumps(data, indent=4)}")
            except:
                print(f"Response Text: {response.text}")
        else:
            print(f"⚠️  API returned status code: {response.status_code}")
            print(f"Response: {response.text}")

        return True

    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    test_authenticated_request()