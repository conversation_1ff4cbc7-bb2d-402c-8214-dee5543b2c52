"""
Telegram bot handler for session management commands.
"""

import logging
import async<PERSON>
from typing import Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler,
    ContextTypes, MessageHandler, filters
)

from config import config
from session.session_manager import session_manager_instance

logger = logging.getLogger(__name__)
# Disable httpx library's info logging to reduce verbosity
logging.getLogger("httpx").setLevel(logging.WARNING)


class TelegramBotHandler:
    """Handles Telegram bot interactions for session management."""

    def __init__(self):
        self.app = None
        self.pending_biometric_url: Optional[str] = None

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command."""
        welcome_msg = (
            "🧠 *BrainSpace Session Manager*\n\n"
            "Available commands:\n"
            "• /login - Start authentication process\n"
            "• /status - Check session status\n"
            "• /help - Show this help message\n\n"
            "I'll help you manage your Brain API sessions!"
        )
        await update.message.reply_text(welcome_msg, parse_mode='Markdown')

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command."""
        help_msg = (
            "🔧 *BrainSpace Commands*\n\n"
            "*Authentication:*\n"
            "• /login - Start new authentication\n"
            "• /status - Check current session\n\n"
            "*Session Management:*\n"
            "• Sessions expire automatically\n"
            "• You'll get notifications before expiry\n"
            "• Use /login to refresh expired sessions\n\n"
            "*Need help?* Contact your administrator."
        )
        await update.message.reply_text(help_msg, parse_mode='Markdown')

    async def login_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /login command."""
        # # Check if a valid session already exists
        # current_session = session_manager_instance.get_session()
        # if current_session:
        #     status = session_manager_instance.get_session_status()
        #     await update.message.reply_text(
        #         "✅ *You are already logged in!*\n\n"
        #         f"📅 Expires: {status['expires_at']}\n"
        #         f"⏰ Time remaining: {status['time_remaining']}",
        #         parse_mode='Markdown'
        #     )
        #     return

        await update.message.reply_text("🔐 Starting authentication...")

        # Attempt authentication
        auth_response = session_manager_instance.authenticate()

        if auth_response.success:
            await update.message.reply_text(
                "✅ *Authentication successful!*\n"
                f"Session expires: {auth_response.expiry.strftime('%Y-%m-%d %H:%M:%S')}",
                parse_mode='Markdown'
            )

        elif auth_response.requires_biometric:
            # Store the biometric URL for completion
            self.pending_biometric_url = auth_response.biometric_url

            # Create inline keyboard for biometric completion
            keyboard = [
                [InlineKeyboardButton("🔗 Open Biometric Link", url=auth_response.biometric_url)],
                [InlineKeyboardButton("🔐 Complete Biometric Auth", callback_data="complete_biometric")],
                [InlineKeyboardButton("❌ Cancel", callback_data="cancel_auth")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                "🔒 *Biometric Authentication Required*\n\n"
                "Please complete the biometric verification using the link below, then click 'Complete Biometric Auth':",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        else:
            await update.message.reply_text(
                f"❌ *Authentication failed*\n"
                f"Error: {auth_response.error}",
                parse_mode='Markdown'
            )

    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command."""
        status = session_manager_instance.get_session_status()

        if status['authenticated']:
            status_msg = (
                "✅ *Session Active*\n\n"
                f"📅 Expires: {status['expires_at']}\n"
                f"⏰ Time remaining: {status['time_remaining']}\n"
                f"📊 Status: {status['status']}"
            )
        else:
            status_msg = (
                "❌ *No Active Session*\n\n"
                f"Status: {status['status']}\n"
                "Use /login to authenticate."
            )

        await update.message.reply_text(status_msg, parse_mode='Markdown')

    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline button callbacks."""
        query = update.callback_query
        await query.answer()

        if query.data == "complete_biometric":
            if self.pending_biometric_url:
                await query.edit_message_text("🔄 Completing biometric authentication...")

                # Complete biometric auth
                auth_response = session_manager_instance.complete_biometric_auth(
                    self.pending_biometric_url
                )

                if auth_response.success:
                    await query.edit_message_text(
                        "✅ *Biometric Authentication Successful!*\n"
                        f"Session expires: {auth_response.expiry.strftime('%Y-%m-%d %H:%M:%S')}",
                        parse_mode='Markdown'
                    )
                else:
                    await query.edit_message_text(
                        f"❌ *Biometric Authentication Failed*\n"
                        f"Error: {auth_response.error}",
                        parse_mode='Markdown'
                    )

                self.pending_biometric_url = None
            else:
                await query.edit_message_text("❌ No pending biometric authentication.")

        elif query.data == "cancel_auth":
            self.pending_biometric_url = None
            await query.edit_message_text("❌ Authentication cancelled.")

    async def send_notification(self, message: str):
        """Send notification message to user."""
        try:
            if self.app:
                await self.app.bot.send_message(
                    chat_id=config.TG_CHAT_ID,
                    text=message,
                    parse_mode='Markdown'
                )
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")

    def setup_handlers(self):
        """Setup command and callback handlers."""
        if not self.app:
            return

        # Command handlers
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("help", self.help_command))
        self.app.add_handler(CommandHandler("login", self.login_command))
        self.app.add_handler(CommandHandler("status", self.status_command))

        # Callback query handler
        self.app.add_handler(CallbackQueryHandler(self.button_callback))

    async def start_bot(self):
        """Start the Telegram bot."""
        try:
            # Create application
            self.app = Application.builder().token(config.TG_BOT_TOKEN).build()

            # Setup handlers
            self.setup_handlers()

            # Start the bot
            logger.info("Starting Telegram bot...")
            await self.app.initialize()
            await self.app.start()
            await self.app.updater.start_polling()

            logger.info("Telegram bot started successfully")

        except Exception as e:
            logger.error(f"Failed to start Telegram bot: {e}")
            raise

    async def stop_bot(self):
        """Stop the Telegram bot."""
        if self.app:
            logger.info("Stopping Telegram bot...")
            await self.app.updater.stop()
            await self.app.stop()
            await self.app.shutdown()
            logger.info("Telegram bot stopped")


# Global bot handler instance
bot_handler = TelegramBotHandler()
