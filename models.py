from datetime import datetime
from typing import Optional
from enum import Enum
from pydantic import BaseModel, Field

class AlphaStatus(str, Enum):
    NONE = "none"
    SIMULATING = "simulating"
    FINISH = "finish"
    ERROR = "error"

class Template(BaseModel):
    id: Optional[int] = None
    template: str
    parameter_space: Optional[dict] = None  # JSONB type in PostgreSQL
    settings_space: Optional[dict] = None  # JSONB type in PostgreSQL
    created_at: datetime = Field(default_factory=datetime.now)

class Alpha(BaseModel):
    id: Optional[int] = None
    alpha: str
    settings: Optional[dict] = None  # JSONB type in PostgreSQL
    status: AlphaStatus = AlphaStatus.NONE
    simulation_id: Optional[str] = None
    alpha_id: Optional[str] = None
    result: Optional[dict] = None  # JSONB type in PostgreSQL
    error: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    simulate_at: Optional[datetime] = None
    last_check: Optional[datetime] = None
    result_at: Optional[datetime] = None

class AlphaReference(BaseModel):
    id: Optional[int] = None
    alpha_id: int
    template_id: int
